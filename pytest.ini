[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --html=reports/test-report.html
    --self-contained-html
    --junit-xml=reports/junit.xml

markers =
    smoke: Quick smoke tests for basic functionality
    integration: Integration tests that require full system
    slow: Tests that take longer to run
    permissions: Tests related to user permissions and roles
    messaging: Tests for core messaging functionality
    search: Tests for search functionality
    admin: Tests requiring admin privileges
    ui: User interface tests
    api: API-related tests (if applicable)

filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Timeout for tests (in seconds)
timeout = 300
