name: CI/CD Pipeline

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]

jobs:
  lint-and-validate:
    runs-on: ubuntu-latest
    name: <PERSON><PERSON> and Validate Documentation
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: |
        npm install -g markdownlint-cli
        npm install -g @cucumber/cucumber
        
    - name: Lint Markdown files
      run: |
        markdownlint docs/**/*.md README.md Specification_Messaging.md || true
        
    - name: Validate Gherkin syntax
      run: |
        npx cucumber-js --dry-run docs/gherkin/**/*.feature || true
        
    - name: Check file structure
      run: |
        echo "Checking repository structure..."
        ls -la
        echo "Documentation structure:"
        find docs -type f -name "*.md" | head -20

  setup-test-environment:
    runs-on: ubuntu-latest
    name: Setup Test Environment
    needs: lint-and-validate
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install selenium pytest pytest-html pytest-xvfb webdriver-manager
        pip install requests beautifulsoup4 faker
        
    - name: Setup Chrome
      uses: browser-actions/setup-chrome@latest
      
    - name: Setup ChromeDriver
      uses: nanasess/setup-chromedriver@master
      
    - name: Verify test setup
      run: |
        python --version
        pip list | grep selenium
        chromedriver --version
        google-chrome --version

  unit-tests:
    runs-on: ubuntu-latest
    name: Unit Tests
    needs: setup-test-environment
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install selenium pytest pytest-html pytest-xvfb webdriver-manager
        pip install requests beautifulsoup4 faker
        
    - name: Run unit tests
      run: |
        if [ -d "tests/unit" ]; then
          python -m pytest tests/unit/ -v --html=reports/unit-test-report.html --self-contained-html
        else
          echo "No unit tests found, creating placeholder..."
          mkdir -p tests/unit
          echo "# Unit tests will be implemented here" > tests/unit/README.md
        fi
        
    - name: Upload unit test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: unit-test-results
        path: reports/

  integration-tests:
    runs-on: ubuntu-latest
    name: Integration Tests (Selenium)
    needs: setup-test-environment
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install selenium pytest pytest-html pytest-xvfb webdriver-manager
        pip install requests beautifulsoup4 faker
        
    - name: Setup Chrome
      uses: browser-actions/setup-chrome@latest
      
    - name: Setup ChromeDriver
      uses: nanasess/setup-chromedriver@master
      
    - name: Run Selenium tests
      run: |
        export DISPLAY=:99
        Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
        if [ -d "tests/selenium" ]; then
          python -m pytest tests/selenium/ -v --html=reports/selenium-test-report.html --self-contained-html
        else
          echo "Selenium tests directory not found, will be created in next step"
        fi
        
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: selenium-test-results
        path: reports/

  security-scan:
    runs-on: ubuntu-latest
    name: Security Scan
    needs: lint-and-validate
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  deploy-docs:
    runs-on: ubuntu-latest
    name: Deploy Documentation
    needs: [unit-tests, integration-tests]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'

    permissions:
      contents: read
      pages: write
      id-token: write

    steps:
    - uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install documentation tools
      run: |
        npm install -g @mermaid-js/mermaid-cli
        npm install -g markdown-to-html-cli

    - name: Create documentation site
      run: |
        mkdir -p docs-site

        # Create index page
        cat > docs-site/index.html << 'EOF'
        <!DOCTYPE html>
        <html>
        <head>
            <title>Messaging Epic Documentation</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; }
                .header { background: #f6f8fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
                .nav { background: #0969da; color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
                .nav a { color: white; text-decoration: none; margin-right: 20px; }
                .section { margin-bottom: 30px; padding: 20px; border: 1px solid #d1d9e0; border-radius: 8px; }
                .badge { background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🚀 Messaging Epic Documentation</h1>
                <p>Comprehensive documentation for the SkilledUp.Life messaging system</p>
                <span class="badge">TDD Ready</span>
            </div>

            <div class="nav">
                <a href="#overview">Overview</a>
                <a href="#specifications">Specifications</a>
                <a href="#tests">Test Suite</a>
                <a href="#development">Development Guide</a>
                <a href="test-reports/">Test Reports</a>
            </div>

            <div class="section" id="overview">
                <h2>📋 Project Overview</h2>
                <p>This project implements a comprehensive messaging system with role-based permissions, admin controls, and advanced features like search and conversation grouping.</p>
                <ul>
                    <li><strong>Repository:</strong> <a href="https://github.com/jerryagenyi/sul-messaging-epic">sul-messaging-epic</a></li>
                    <li><strong>Test Coverage:</strong> 6 core features with comprehensive Selenium tests</li>
                    <li><strong>Development Approach:</strong> Test-Driven Development (TDD)</li>
                </ul>
            </div>

            <div class="section" id="specifications">
                <h2>📖 Specifications</h2>
                <ul>
                    <li><a href="specs/messaging-epic/">Feature Specifications</a></li>
                    <li><a href="gherkin/messaging-epic/">Gherkin Test Scenarios</a></li>
                    <li><a href="testing/messaging-epic/">Test Matrix</a></li>
                </ul>
            </div>

            <div class="section" id="tests">
                <h2>🧪 Test Suite</h2>
                <p>Comprehensive Selenium-based test suite covering all messaging functionality:</p>
                <ul>
                    <li>✅ Role-based Permission Tests</li>
                    <li>✅ Admin Settings Management</li>
                    <li>✅ Profile Message Buttons</li>
                    <li>✅ Core Messaging Interface</li>
                    <li>✅ Message Search Functionality</li>
                    <li>✅ Conversation Grouping</li>
                </ul>
                <p><a href="test-reports/">View Latest Test Reports →</a></p>
            </div>

            <div class="section" id="development">
                <h2>👨‍💻 Development Guide</h2>
                <p>Ready to start development? Follow our TDD approach:</p>
                <ol>
                    <li>🔴 <strong>RED:</strong> Run tests (they fail initially)</li>
                    <li>🟢 <strong>GREEN:</strong> Implement minimal code to pass tests</li>
                    <li>🔵 <strong>REFACTOR:</strong> Improve code while keeping tests passing</li>
                </ol>
                <p><a href="TDD_DEVELOPMENT_GUIDE.html">View Complete Development Guide →</a></p>
            </div>
        </body>
        </html>
        EOF

    - name: Convert markdown files to HTML
      run: |
        # Convert main documentation files
        if [ -f "README.md" ]; then
          markdown-to-html README.md -o docs-site/README.html
        fi

        if [ -f "TDD_DEVELOPMENT_GUIDE.md" ]; then
          markdown-to-html TDD_DEVELOPMENT_GUIDE.md -o docs-site/TDD_DEVELOPMENT_GUIDE.html
        fi

        # Copy documentation structure
        cp -r docs docs-site/ 2>/dev/null || true

        # Copy test reports if they exist
        mkdir -p docs-site/test-reports
        cp -r reports/* docs-site/test-reports/ 2>/dev/null || true

    - name: Setup Pages
      uses: actions/configure-pages@v3

    - name: Upload artifact
      uses: actions/upload-pages-artifact@v2
      with:
        path: './docs-site'

    - name: Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@v2
