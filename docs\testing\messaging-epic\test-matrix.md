# Messaging Epic Test Matrix

**Repository:** https://github.com/jerryagenyi/sul-messaging-epic

| Feature | What | Why | How | Edge Cases | Acceptance Criteria | Suggested Test Name |
|---------|------|-----|-----|------------|-------------------|-------------------|
| Role Permissions | Enforce messaging rules | Prevent unauthorized communication | Check user role vs target permissions | Role changes mid-session | Correct button visibility, message sending blocked/allowed | `test_messaging_permissions_enforced()` |
| Admin Settings | Configure messaging rules | Allow platform control | Admin dashboard with setting toggles | Invalid configurations | Settings save and apply immediately | `test_admin_messaging_settings()` |
| Profile Buttons | Initiate conversations | Easy message access | Button on profiles opens messaging | Permission changes | But<PERSON> appears/disappears correctly | `test_profile_message_button()` |
| Message Interface | Send/receive messages | Core communication | Real-time messaging with search | Network failures, large histories | Messages delivered, search works | `test_messaging_interface()` |
| Search Function | Find conversations | Message retrieval | Text search across messages | Empty results, special characters | Relevant results returned | `test_message_search()` |
| Conversation Grouping | Organize messages | Better UX | Group by participant | Multiple conversations | Messages grouped correctly | `test_conversation_grouping()` |

## GitHub Repository Structure
```
sul-messaging-epic/
├── docs/
│   ├── specs/messaging-epic/
│   ├── gherkin/messaging-epic/
│   └── testing/messaging-epic/
├── src/
├── tests/
└── .github/workflows/
```
